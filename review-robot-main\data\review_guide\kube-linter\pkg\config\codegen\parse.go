package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"text/template"

	"golang.stackrox.io/kube-linter/internal/utils"

	"github.com/Masterminds/sprig/v3"
	"github.com/pkg/errors"
	"k8s.io/gengo/parser"
	"k8s.io/gengo/types"
)

const (
	metadataMarker   = "+"
	flagNameTagKey   = "flagName"
	paramsStructName = "Config"
)

const (
	fileTemplateStr = `// Code generated by kube-linter flag codegen. DO NOT EDIT.
// +build !flagcodegen

package config

import (
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// AddFlags, walks through config.Check struct and bind its Member to Cobra command 
// and add respective Viper flag 
func AddFlags(c *cobra.Command, v *viper.Viper) {
	{{- range . }}
	c.Flags().{{ .FlagDesc.CobraType }}("{{ .FlagDesc.Name }}", {{ defaultVal .FlagDesc.CobraType }}, "{{ .FlagDesc.Description }}")
	if err := v.BindPFlag("{{ .FlagDesc.JSONPath }}", c.Flags().Lookup("{{ .FlagDesc.Name }}")); err != nil {
		panic(err)
	}

	{{- end }}
}
`
)

var (
	fileTemplate = template.Must(
		template.New("gen").
			Funcs(sprig.TxtFuncMap()).
			Funcs(template.FuncMap{"defaultVal": mustGetDefaultValueRepr}).
			Parse(fileTemplateStr),
	)
)

// These types are used in template to add appropriate Cobra flags
var (
	stringType  = newFlagType("String", `""`)
	intType     = newFlagType("Int", "0")
	boolType    = newFlagType("Bool", "false")
	float32Type = newFlagType("Float32", "0")
	float64Type = newFlagType("Float64", "0")

	stringSliceType = newFlagType("StringSlice", "nil")

	defaultValueReprsByTypeName = make(map[string]string)
)

func mustGetDefaultValueRepr(typeName string) string {
	valueRepr := defaultValueReprsByTypeName[typeName]
	if valueRepr == "" {
		panic(fmt.Sprintf("couldn't represent default value for type %s", typeName))
	}
	return valueRepr
}

func newFlagType(typeName, defaultValueRepr string) string {
	defaultValueReprsByTypeName[typeName] = defaultValueRepr
	return typeName
}

type flagDesc struct {
	Name        string
	JSONPath    string
	Description string
	CobraType   string
}

type templateElem struct {
	FlagDesc flagDesc
}

func getJSONKey(member types.Member) (string, error) {
	jsonTag := reflect.StructTag(member.Tags).Get("json")
	if jsonTag == "" {
		return "", errors.Errorf("member %v does not specify a json tag", member)
	}
	return strings.Split(jsonTag, ",")[0], nil
}

func getDescription(member types.Member) string {
	firstCommentLineWithMetadata := len(member.CommentLines)
	for i, commentLine := range member.CommentLines {
		if strings.HasPrefix(commentLine, metadataMarker) {
			firstCommentLineWithMetadata = i
			break
		}
	}
	return strings.Join(member.CommentLines[:firstCommentLineWithMetadata], " ")
}

func getCobraTypeFromMember(memberType *types.Type) string {
	switch memberType.Kind {
	case types.Builtin:
		switch memberType {
		case types.String:
			return stringType
		case types.Int:
			return intType
		case types.Float32:
			return float32Type
		case types.Float64:
			return float64Type
		case types.Bool:
			return boolType
		}
	case types.Slice:
		if memberType.Elem == types.String {
			return stringSliceType
		}
	}
	return ""
}

func constructFlagDescsFromStruct(typeSpec *types.Type, jsonPathSoFar []string) ([]flagDesc, error) {
	var flagDescs []flagDesc
	for _, member := range types.FlattenMembers(typeSpec.Members) {
		extractedTags := types.ExtractCommentTags("+", member.CommentLines)
		flagNameTags := extractedTags[flagNameTagKey]
		if len(flagNameTags) > 1 {
			return nil, errors.Errorf("got multiple flag name tags in member: %v", member)
		}
		var flagName string
		if len(flagNameTags) == 1 {
			flagName = flagNameTags[0]
		}
		if flagName == "-" {
			continue
		}

		jsonKey, err := getJSONKey(member)
		if err != nil {
			return nil, err
		}

		jsonPath := append([]string{}, jsonPathSoFar...)
		jsonPath = append(jsonPath, jsonKey)

		if member.Type.Kind == types.Struct {
			subDescs, err := constructFlagDescsFromStruct(member.Type, jsonPath)
			if err != nil {
				return nil, errors.Wrapf(err, "handling field %s", member.Name)
			}
			flagDescs = append(flagDescs, subDescs...)
			continue
		}

		// If we got here, it must be a primitive field.
		cobraType := getCobraTypeFromMember(member.Type)
		if cobraType == "" {
			return nil, errors.Errorf("couldn't find mapped cobra type for member %v", member)
		}
		flagDescs = append(flagDescs, flagDesc{
			Name:        flagName,
			JSONPath:    strings.Join(jsonPath, "."),
			Description: getDescription(member),
			CobraType:   cobraType,
		})
	}
	return flagDescs, nil
}

func mainCmd() error {
	flag.Parse()
	outFileName := flag.Arg(0)
	if outFileName == "" {
		return errors.New("out file name not specified")
	}
	b := parser.New()

	// This avoids parsing generated files in the package (since we add +build !flagcodegen to them,
	// which makes the parsing much quicker since the parser doesn't have to load any imported packages).
	b.AddBuildTags("flagcodegen")
	if err := b.AddDir("."); err != nil {
		return err
	}
	typeUniverse, err := b.FindTypes()
	if err != nil {
		return err
	}

	pkgNames := b.FindPackages()
	if len(pkgNames) != 1 {
		return errors.Errorf("found unexpected number of packages in %+v: %d", pkgNames, len(pkgNames))
	}

	pkg := typeUniverse.Package(pkgNames[0])
	paramsType := pkg.Type(paramsStructName)

	flagDescs, err := constructFlagDescsFromStruct(paramsType, nil)
	if err != nil {
		return err
	}

	var templateObj []templateElem

	for _, flagDesc := range flagDescs {
		buf := bytes.NewBuffer(nil)
		enc := json.NewEncoder(buf)
		enc.SetIndent("", "\t")
		if err := enc.Encode(flagDesc); err != nil {
			return errors.Wrapf(err, "couldn't marshal param %v", flagDesc)
		}

		templateObj = append(templateObj, templateElem{
			FlagDesc: flagDesc,
		})
	}
	outF, err := os.Create(filepath.Clean(outFileName))
	if err != nil {
		return errors.Wrap(err, "creating output file")
	}
	defer utils.IgnoreError(outF.Close)
	if err := fileTemplate.Execute(outF, templateObj); err != nil {
		return errors.Wrap(err, "Writing template to File")
	}
	return nil
}

func main() {
	if err := mainCmd(); err != nil {
		fmt.Printf("Error executing command: %v", err)
		os.Exit(1)
	}
}
